<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$user_role = $_SESSION["role"];

// Get user name for display
$user_query = "SELECT full_name FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
$user_name = $user_data['full_name'] ?? 'User';

// Check if venue_id is provided in URL (for direct venue access)
$selected_venue_id = isset($_GET['venue_id']) ? intval($_GET['venue_id']) : null;
$selected_venue = null;

if ($selected_venue_id) {
    $venue_query = "SELECT * FROM venues WHERE venueid = ?";
    $stmt = mysqli_prepare($conn, $venue_query);
    mysqli_stmt_bind_param($stmt, "i", $selected_venue_id);
    mysqli_stmt_execute($stmt);
    $venue_result = mysqli_stmt_get_result($stmt);
    $selected_venue = mysqli_fetch_assoc($venue_result);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Scan Venue QR Code</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 15px;
            transition: margin-left 0.3s ease;
        }
        
        /* Adjust container when sidebar is open */
        #menuToggle:checked ~ .container {
            margin-left: 250px;
        }
        
        @media (max-width: 768px) {
            #menuToggle:checked ~ .container {
                margin-left: 0;
                opacity: 0.4;
            }
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .scanner-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 25px;
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            font-size: 18px;
            font-weight: 500;
            text-align: center;
        }
        
        .card-body {
            padding: 30px;
        }
        
        .scanner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .camera-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
            max-width: 400px;
        }
        

        
        #qr-video-container {
            width: 100%;
            max-width: 400px;
            height: 400px;
            border-radius: 12px;
            overflow: hidden;
            border: 3px solid #ddd;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        #qr-reader {
            width: 100%;
            height: 100%;
        }

        #qr-reader video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .scanner-placeholder {
            text-align: center;
            color: #666;
            font-size: 16px;
        }
        
        .scanner-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .scan-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            min-width: 200px;
        }
        
        .scan-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .scan-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .scan-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            display: none;
        }
        
        .scan-result.success {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .scan-result.error {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
        }
        
        .scan-result.info {
            background: rgba(52, 152, 219, 0.1);
            color: var(--info-color);
            border: 1px solid var(--info-color);
        }
        
        .manual-entry {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 25px;
        }
        
        .manual-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-group label {
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-group input {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .submit-button {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .submit-button:hover {
            background: #27ae60;
            transform: translateY(-1px);
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: color 0.3s;
        }
        
        .back-button:hover {
            color: var(--primary-dark);
        }
    </style>
</head>
<body>

<?php 
if ($user_role === 'lecturer') {
    include('CSS/lecturersidebar.php');
} else {
    include('CSS/studentsidebar.php');
}
?>

<div class="container">
    <a href="<?php echo ($user_role === 'lecturer') ? 'lecturer_dashboard.php' : 'student_dashboard.php'; ?>" class="back-button">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
    
    <div class="page-header">
        <h1>Scan Venue QR Code</h1>
        <p>Welcome, <?php echo htmlspecialchars($user_name); ?>! Scan a venue QR code to check in.</p>
    </div>

    <?php if ($selected_venue): ?>
    <div class="scanner-card">
        <div class="card-header">
            <i class="fas fa-building"></i> Selected Venue: <?php echo htmlspecialchars($selected_venue['venuename']); ?>
        </div>
        <div class="card-body">
            <div class="scanner-container">
                <p><strong>Location:</strong> <?php echo htmlspecialchars($selected_venue['location'] ?? $selected_venue['venuedescription']); ?></p>
                <form action="process_venue_scan.php" method="post" class="manual-form">
                    <input type="hidden" name="venue_id" value="<?php echo $selected_venue['venueid']; ?>">
                    <input type="hidden" name="action" value="checkin">
                    <button type="submit" class="submit-button">
                        <i class="fas fa-sign-in-alt"></i> Check In to This Venue
                    </button>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="scanner-card">
        <div class="card-header">
            <i class="fas fa-qrcode"></i> QR Code Scanner
        </div>
        <div class="card-body">
            <div class="scanner-container">
                <div class="camera-controls">
                    <button id="scan-button" class="scan-button">
                        <i class="fas fa-qrcode"></i> Start Scanning
                    </button>
                </div>

                <div id="qr-video-container">
                    <div id="scanner-placeholder" class="scanner-placeholder">
                        <i class="fas fa-camera"></i>
                        <p>Loading camera... Please wait</p>
                    </div>
                    <div id="qr-reader" style="display: none;"></div>
                </div>

                <div id="scan-result" class="scan-result"></div>
            </div>
        </div>
    </div>

    <div class="manual-entry">
        <div class="card-header">
            <i class="fas fa-keyboard"></i> Manual Entry
        </div>
        <div class="card-body">
            <form action="process_venue_scan.php" method="post" class="manual-form">
                <div class="form-group">
                    <label for="venue_code">Venue Code or ID:</label>
                    <input type="text" id="venue_code" name="venue_code" placeholder="Enter venue code or scan result" required>
                </div>
                <input type="hidden" name="action" value="checkin">
                <button type="submit" class="submit-button">
                    <i class="fas fa-sign-in-alt"></i> Check In
                </button>
            </form>
        </div>
    </div>
</div>

<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scanButton = document.getElementById('scan-button');
    const scanResult = document.getElementById('scan-result');
    const videoContainer = document.getElementById('qr-video-container');
    const qrReader = document.getElementById('qr-reader');
    const placeholder = document.getElementById('scanner-placeholder');

    let html5QrCode;
    let isScanning = false;
    let availableCameras = [];
    let currentCameraId = null;

    // Debug function
    function debugLog(message) {
        console.log(`[QR Scanner] ${new Date().toLocaleTimeString()}: ${message}`);
    }

    // Initialize QR scanner
    try {
        html5QrCode = new Html5Qrcode("qr-reader");
        debugLog('QR scanner initialized successfully');

    } catch (error) {
        console.error('Error initializing QR scanner:', error);
        showResult('Error initializing camera. Please check permissions.', 'error');
        return;
    }

    // Get available cameras and auto-select the best one
    Html5Qrcode.getCameras().then(devices => {
        if (devices && devices.length) {
            availableCameras = devices;
            debugLog(`Found ${devices.length} cameras`);

            // Auto-select the best camera (prefer back camera for mobile)
            currentCameraId = selectBestCamera(devices);
            debugLog(`Auto-selected camera: ${currentCameraId}`);

            // Update placeholder text
            placeholder.innerHTML = '<i class="fas fa-camera"></i><p>Camera ready! Click "Start Scanning" to begin</p>';

        } else {
            showResult('No cameras found. Please check camera permissions.', 'error');
            debugLog('No cameras available');
        }
    }).catch(err => {
        console.error('Error getting cameras:', err);
        showResult('Error accessing cameras. Please check permissions.', 'error');
        debugLog('Error getting cameras: ' + err);
    });

    // Function to select the best available camera
    function selectBestCamera(devices) {
        // Prefer back camera (environment) for mobile devices
        const backCamera = devices.find(device =>
            device.label.toLowerCase().includes('back') ||
            device.label.toLowerCase().includes('environment') ||
            device.label.toLowerCase().includes('rear')
        );

        if (backCamera) {
            debugLog('Selected back camera: ' + backCamera.label);
            return backCamera.id;
        }

        // Otherwise, use the first available camera
        debugLog('Selected first available camera: ' + devices[0].label);
        return devices[0].id;
    }

    // Handle scan button click
    scanButton.addEventListener('click', function() {
        if (isScanning) {
            stopScanning();
        } else {
            startScanning();
        }
    });

    function startScanning() {
        if (!currentCameraId) {
            showResult('No camera available. Please check camera permissions.', 'error');
            return;
        }

        debugLog('Starting scan with camera: ' + currentCameraId);

        // Simplified, stable configuration
        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0
        };

        // Show loading state
        scanButton.disabled = true;
        scanButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting Camera...';
        showResult('Starting camera...', 'info');

        html5QrCode.start(
            currentCameraId,
            config,
            (decodedText) => {
                // On successful scan
                console.log('QR Code scanned:', decodedText);
                showResult('QR Code detected! Processing...', 'success');
                // Don't stop scanning immediately, let user see the success message
                setTimeout(() => {
                    stopScanning();
                    processQrCode(decodedText);
                }, 1000);
            },
            (errorMessage) => {
                // Ignore scanning errors (they happen frequently during scanning)
                // Only log severe errors
                if (errorMessage.includes('NotFoundException') === false) {
                    console.debug('Scan error:', errorMessage);
                }
            }
        ).then(() => {
            isScanning = true;
            scanningStartTime = Date.now();
            scanButton.disabled = false;
            scanButton.innerHTML = '<i class="fas fa-stop"></i> Stop Scanning';
            placeholder.style.display = 'none';
            qrReader.style.display = 'block';
            showResult('Camera active. Point at QR code to scan.', 'info');
            debugLog('Camera started successfully');

        }).catch(err => {
            console.error('Error starting scanner:', err);
            scanButton.disabled = false;
            scanButton.innerHTML = '<i class="fas fa-qrcode"></i> Start Scanning';

            // More specific error messages
            let errorMsg = 'Error starting camera: ';
            if (err.toString().includes('NotAllowedError')) {
                errorMsg += 'Camera permission denied. Please allow camera access and try again.';
            } else if (err.toString().includes('NotFoundError')) {
                errorMsg += 'No camera found. Please check your camera connection.';
            } else if (err.toString().includes('NotReadableError')) {
                errorMsg += 'Camera is already in use by another application.';
            } else {
                errorMsg += err.toString();
            }

            showResult(errorMsg, 'error');
        });
    }

    function stopScanning() {
        if (html5QrCode && isScanning) {
            scanButton.disabled = true;
            scanButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Stopping...';

            html5QrCode.stop().then(() => {
                isScanning = false;
                scanButton.disabled = false;
                scanButton.innerHTML = '<i class="fas fa-qrcode"></i> Start Scanning';
                placeholder.style.display = 'block';
                qrReader.style.display = 'none';
                hideResult();
                debugLog('Camera stopped successfully');
            }).catch(err => {
                console.error('Error stopping scanner:', err);
                // Reset button state even if stop fails
                isScanning = false;
                scanButton.disabled = false;
                scanButton.innerHTML = '<i class="fas fa-qrcode"></i> Start Scanning';
                placeholder.style.display = 'block';
                qrReader.style.display = 'none';
                showResult('Camera stopped with errors', 'warning');
            });
        }
    }

    // Process QR code data
    function processQrCode(qrData) {
        showResult('Processing QR code...', 'info');

        // Send data to server for processing
        fetch('process_venue_scan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `venue_code=${encodeURIComponent(qrData)}&action=checkin`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult(data.message, 'success');
                // Reload page after 3 seconds to update data
                setTimeout(() => {
                    window.location.href = '<?php echo ($user_role === "lecturer") ? "lecturer_dashboard.php" : "student_dashboard.php"; ?>';
                }, 3000);
            } else {
                showResult(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error processing QR code:', error);
            showResult('Error processing QR code. Please try again.', 'error');
        });
    }

    function showResult(message, type) {
        scanResult.className = `scan-result ${type}`;
        scanResult.innerHTML = message;
        scanResult.style.display = 'block';
    }

    function hideResult() {
        scanResult.style.display = 'none';
    }

    // Prevent page visibility changes from stopping the camera
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            debugLog('Page hidden - camera may pause');
        } else {
            debugLog('Page visible - camera should resume');
            if (isScanning && html5QrCode) {
                // Check if camera is still running
                setTimeout(() => {
                    if (isScanning && qrReader.style.display === 'block') {
                        debugLog('Checking camera state after visibility change');
                    }
                }, 1000);
            }
        }
    });

    // Handle page unload
    window.addEventListener('beforeunload', function() {
        if (isScanning && html5QrCode) {
            debugLog('Page unloading - stopping camera');
            html5QrCode.stop().catch(err => console.error('Error stopping camera on unload:', err));
        }
    });
});
</script>

</body>
</html>
