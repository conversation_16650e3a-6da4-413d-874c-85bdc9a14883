-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 07, 2025 at 10:25 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `project`
--

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_requests`
--

CREATE TABLE `password_reset_requests` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `token` varchar(64) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `used` tinyint(1) NOT NULL DEFAULT 0,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reports`
--

CREATE TABLE `reports` (
  `report_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `venue_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `reported_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reports`
--

INSERT INTO `reports` (`report_id`, `user_id`, `venue_id`, `description`, `status`, `reported_at`) VALUES
(1, 11, 17, 'LIGHTs are not working', 'resolved', '2025-05-26 07:59:52'),
(2, 2, 15, 'not working', 'resolved', '2025-05-26 08:00:34');

-- --------------------------------------------------------

--
-- Table structure for table `timetables`
--

CREATE TABLE `timetables` (
  `id` int(11) NOT NULL,
  `course` varchar(50) DEFAULT NULL,
  `year` int(11) DEFAULT NULL,
  `stream` varchar(10) DEFAULT NULL,
  `day` varchar(10) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `type` varchar(30) DEFAULT NULL,
  `subject_code` varchar(50) DEFAULT NULL,
  `venue` varchar(50) DEFAULT NULL,
  `lecturer` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `timetables`
--

INSERT INTO `timetables` (`id`, `course`, `year`, `stream`, `day`, `start_time`, `end_time`, `type`, `subject_code`, `venue`, `lecturer`) VALUES
(1, 'BSc IT', 1, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'ITU_07209', 'TH_A', 'Kwesigabo, E. (Dr)'),
(2, 'BSc IT', 1, 'A', 'Monday', '10:00:00', '11:00:00', 'Tutorial', 'G1_ITU 07208', 'LAB_5', 'Siphy, A. S (Mr)'),
(3, 'BSc IT', 1, 'B', 'Tuesday', '11:00:00', '12:00:00', 'Lecture', 'ITU_07207', 'TH_A', 'Bakiri, A.H (Dr)'),
(4, 'BSc IT', 1, 'B', 'Wednesday', '12:00:00', '13:00:00', 'Tutorial', 'G2_ITU 07209', 'LAB_4', 'Kwesigabo, E. (Dr)'),
(5, 'BSc IT', 2, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'CSU_07426', 'TH_A', 'Nagunwa, T (Dr)'),
(6, 'BSc IT', 2, 'B', 'Tuesday', '10:00:00', '11:00:00', 'Practical_Lab', 'G2_ITU 07421', 'LAB_8', 'Ngowi, J (Mr)'),
(7, 'BSc IT', 2, 'A', 'Wednesday', '11:00:00', '12:00:00', 'Tutorial', 'G4_ITU 07425', 'LAB_8', 'Mutama, S (Mr)'),
(8, 'BSc IT', 2, 'B', 'Thursday', '13:00:00', '14:00:00', 'Lecture', 'ITU_07422', 'TH_B', 'Massa, D (Dr)'),
(9, 'BSc IT', 3, 'SysDev', 'Monday', '09:00:00', '10:00:00', 'Tutorial', 'ITU/CSU_08213', 'LAB_4', 'Shefa, F'),
(10, 'BSc IT', 3, 'SysAdmin', 'Tuesday', '10:00:00', '11:00:00', 'Lecture', 'CSU_08221', 'TH_B', 'Miku, H (Mr)'),
(11, 'BSc IT', 3, 'SysDev', 'Wednesday', '14:00:00', '15:00:00', 'Lecture', 'CSU_08220', 'TH_B', 'Mushi, R (Dr)'),
(12, 'ODIT', 1, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'ITT_05211', 'LAB_1', 'Ngowi, J (Mr)'),
(13, 'ODIT', 1, 'A', 'Tuesday', '10:00:00', '11:00:00', 'Tutorial', 'ITT_05210', 'LAB_1', 'Ngeleshi, R (Mr)'),
(14, 'ODIT', 2, 'A', 'Wednesday', '09:00:00', '10:00:00', 'Lecture', 'ITT_06212', 'LAB_1', 'Byanyuma, M (Dr)'),
(15, 'ODIT', 2, 'A', 'Friday', '11:00:00', '12:00:00', 'Tutorial', 'ITT_06214', 'LAB_5', 'Bakiri, A.H (Dr)');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(20) DEFAULT 'student',
  `course` varchar(50) DEFAULT NULL,
  `year` int(11) DEFAULT NULL,
  `stream` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `full_name`, `email`, `password`, `role`, `course`, `year`, `stream`) VALUES
(1, 'zorayz abdul', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'admin', '', NULL, NULL),
(2, 'john doe', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'lecturer', '', NULL, NULL),
(5, 'peter', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'student', '', NULL, NULL),
(6, 'ISACK SWAI', '<EMAIL>', '$2y$10$5WXtBZ0UyAm2tojAF0bdmua2oKC0wV/aT0R3DO1qdZQFabKTbih8G', 'student', 'ODIT', 1, 'A'),
(7, 'likilo', '<EMAIL>', '$2y$10$APQURwAoCuRvqXIyBvL1K.i6yv1bhZh.cr5kTZbpIt8zTfNXMzErG', 'student', 'BSc IT', 2, 'A'),
(8, 'likilo', '<EMAIL>', '$2y$10$5l2mDD3gc8wN9x7smCkls.IUtH/43EiD1CjS66yn8O5YWsiwW5oEK', 'student', '0', 2, 'B'),
(10, 'tunu', '<EMAIL>', '$2y$10$u0F2QmhLLxNJ8L7b.zuJ8exfFmTCfrbpd/yU9py4JJ2BkL/C.n.H.', 'student', '0', 3, 'C'),
(11, 'Donatha', '<EMAIL>', '$2y$10$8bdg9.veAhPyRB7j86wApO6TUI6S3jUrvLimfrpnBcWC0b46C9.3.', 'student', 'ODIT', 2, 'B'),
(12, 'petro', '<EMAIL>', '$2y$10$c8LwPUdM59VY/jbuYPeMz.1MWmqc9mEN4H8w4KzMwW6q/O3yUK.N.', 'student', 'BSc IT', 1, 'B');

-- --------------------------------------------------------

--
-- Table structure for table `venues`
--

CREATE TABLE `venues` (
  `venueid` int(11) NOT NULL,
  `venuename` varchar(255) NOT NULL,
  `qrcode` varchar(255) NOT NULL,
  `venuedescription` text DEFAULT NULL,
  `status` enum('free','occupied') NOT NULL DEFAULT 'free'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `venues`
--

INSERT INTO `venues` (`venueid`, `venuename`, `qrcode`, `venuedescription`, `status`) VALUES
(15, 'tt3', '\\Users\\isack/Desktop/qrcode_68126bab4a019.png', 'black', 'free'),
(16, 'rafiki', '\\Users\\isack/Desktop/qr_rafiki.png', 'floor ya pili', 'occupied'),
(17, 'tt tower', '\\Users\\isack/Desktop/qrcode_683212a54fd47.png', 'forth floor', 'free'),
(18, 'RAFIKI', '\\Users\\isack/Desktop/qr_rafiki.png', 'JIPYA', 'free'),
(19, 'TT1', '\\Users\\isack/Desktop/qr_tt1.png', 'BLOCK 1', 'occupied');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `password_reset_requests`
--
ALTER TABLE `password_reset_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `reports`
--
ALTER TABLE `reports`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `venue_id` (`venue_id`);

--
-- Indexes for table `timetables`
--
ALTER TABLE `timetables`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `venues`
--
ALTER TABLE `venues`
  ADD PRIMARY KEY (`venueid`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `password_reset_requests`
--
ALTER TABLE `password_reset_requests`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `reports`
--
ALTER TABLE `reports`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `timetables`
--
ALTER TABLE `timetables`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `venues`
--
ALTER TABLE `venues`
  MODIFY `venueid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `reports`
--
ALTER TABLE `reports`
  ADD CONSTRAINT `reports_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `reports_ibfk_2` FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
