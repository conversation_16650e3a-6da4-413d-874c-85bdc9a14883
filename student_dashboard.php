<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$user_query = "SELECT * FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);

// Get recent check-ins
$checkins_query = "SELECT vc.*, v.venuename, v.location 
                  FROM venue_checkins vc 
                  JOIN venues v ON vc.venue_id = v.venueid 
                  WHERE vc.user_id = ? 
                  ORDER BY vc.check_in_time DESC 
                  LIMIT 5";
$stmt = mysqli_prepare($conn, $checkins_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$checkins_result = mysqli_stmt_get_result($stmt);

// Get upcoming events
$events_query = "SELECT * FROM events 
                WHERE (target_audience = 'all' 
                      OR (target_audience = 'specific' 
                          AND (target_course = 'all' OR target_course = ?) 
                          AND (target_year = 'all' OR target_year = ?) 
                          AND (target_stream = 'all' OR target_stream = ?)))
                AND event_date >= CURDATE() 
                ORDER BY event_date, start_time 
                LIMIT 3";
$stmt = mysqli_prepare($conn, $events_query);
mysqli_stmt_bind_param($stmt, "sss", $user_data['course'], $user_data['year'], $user_data['stream']);
mysqli_stmt_execute($stmt);
$events_result = mysqli_stmt_get_result($stmt);

// Get available venues
$venues_query = "SELECT * FROM venues WHERE status = 'available' LIMIT 5";
$venues_result = mysqli_query($conn, $venues_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        .welcome-header {
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .welcome-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .welcome-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dashboard-card-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <p>No upcoming events</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-calendar-day"></i> Today's Classes</span>
                <a href="student_schedule.php">View Schedule</a>
            </div>
            <div class="dashboard-card-body">
                <?php 
                // Get today's classes
                $today = date('l'); // Current day name (Monday, Tuesday, etc.)
                $classes_query = "SELECT * FROM timetables 
                                 WHERE course = ? AND year = ? AND stream = ? AND day = ?
                                 ORDER BY start_time ASC";
                $stmt = mysqli_prepare($conn, $classes_query);
                mysqli_stmt_bind_param($stmt, "siss", $user_data['course'], $user_data['year'], $user_data['stream'], $today);
                mysqli_stmt_execute($stmt);
                $classes_result = mysqli_stmt_get_result($stmt);
                ?>
                
                <?php if (mysqli_num_rows($classes_result) > 0): ?>
                    <?php while ($class = mysqli_fetch_assoc($classes_result)): ?>
                        <div class="class-item">
                            <div class="class-time">
                                <div><?php echo date("h:i A", strtotime($class['start_time'])); ?></div>
                                <div><?php echo date("h:i A", strtotime($class['end_time'])); ?></div>
                            </div>
                            <div class="class-details">
                                <div class="class-subject"><?php echo htmlspecialchars($class['subject_code']); ?></div>
                                <div class="class-info">
                                    <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($class['venue']); ?></span>
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($class['lecturer']); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <p>You have no classes scheduled for today.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-bell"></i> Notifications</span>
                <a href="notifications.php">View All</a>
            </div>
            <div class="dashboard-card-body">
                <?php 
                // Get notifications
                $notifications_query = "SELECT * FROM notifications 
                                       WHERE user_id = ?
                                       ORDER BY created_at DESC LIMIT 5";
                $stmt = mysqli_prepare($conn, $notifications_query);
                mysqli_stmt_bind_param($stmt, "i", $user_id);
                mysqli_stmt_execute($stmt);
                $notifications_result = mysqli_stmt_get_result($stmt);
                ?>
                
                <?php if (mysqli_num_rows($notifications_result) > 0): ?>
                    <?php while ($notification = mysqli_fetch_assoc($notifications_result)): ?>
                        <div class="notification-item">
                            <div class="notification-title">
                                <?php echo htmlspecialchars($notification['title']); ?>
                            </div>
                            <div class="notification-time">
                                <?php echo date('M d, g:i A', strtotime($notification['created_at'])); ?>
                            </div>
                            <div class="notification-content">
                                <?php echo htmlspecialchars($notification['message']); ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <p>No notifications at this time</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <span><i class="fas fa-building"></i> Available Venues</span>
                <a href="venues.php">View All</a>
            </div>
            <div class="dashboard-card-body">
                <?php if (mysqli_num_rows($venues_result) > 0): ?>
                    <?php while ($venue = mysqli_fetch_assoc($venues_result)): ?>
                        <div class="venue-item">
                            <div class="venue-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="venue-details">
                                <h4 class="venue-name"><?php echo htmlspecialchars($venue['venuename']); ?></h4>
                                <p class="venue-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo htmlspecialchars($venue['location']); ?>
                                </p>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-info-circle"></i>
                        <p>No available venues found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>
