# PowerShell script to import project.sql database
Write-Host "Importing project.sql database..." -ForegroundColor Green
Write-Host "Please enter your MySQL root password when prompted." -ForegroundColor Yellow
Write-Host ""

# Read the SQL file content and pipe it to MySQL
$sqlContent = Get-Content -Path "project.sql" -Raw
$sqlContent | & "C:\xampp\mysql\bin\mysql.exe" -u root -p

Write-Host ""
Write-Host "Import completed!" -ForegroundColor Green
Write-Host "You can now access your application at http://localhost/Mine" -ForegroundColor Cyan
Read-Host "Press Enter to continue..."
